# Système de Médias - Images et Vidéos

## Vue d'ensemble

Le système de médias a été étendu pour supporter à la fois les images et les vidéos dans tous les composants du site. Le composant `MediaDisplay` gère automatiquement le type de média et applique les styles appropriés.

## Utilisation

### Dans projectsData.js

Pour utiliser une vidéo au lieu d'une image pour un projet :

```javascript
{
  id: "mon-projet",
  client: "Mon Client",
  year: 2024,
  src: "videos/ma-video.webm",  // Chemin vers la vidéo
  mediaType: "video",           // Spécifie que c'est une vidéo
  // ... autres propriétés
}
```

Pour une image (comportement par défaut) :

```javascript
{
  id: "mon-projet",
  client: "Mon Client", 
  year: 2024,
  src: "projects/mon-projet/image-principale.webp",
  // mediaType: "image" est optionnel (par défaut)
  // ... autres propriétés
}
```

### Types de médias supportés

- **Images** : `.jpg`, `.jpeg`, `.png`, `.webp`, `.gif`
- **Vidéos** : `.mp4`, `.webm`, `.ogg`, `.mov`

### Détection automatique

Si `mediaType` n'est pas spécifié, le système détecte automatiquement le type basé sur l'extension du fichier.

## Composants mis à jour

### MediaDisplay

Le composant principal qui gère l'affichage des médias :

```jsx
<MediaDisplay
  src="videos/ma-video.webm"
  mediaType="video"
  width={800}
  height={600}
  alt="Description"
  autoPlay={true}
  loop={true}
  muted={true}
/>
```

### Propriétés spécifiques aux vidéos

- `autoPlay` : Lecture automatique (défaut: true)
- `loop` : Lecture en boucle (défaut: true)
- `muted` : Vidéo muette (défaut: true)
- `controls` : Afficher les contrôles (défaut: false)
- `playsInline` : Lecture inline sur mobile (défaut: true)

## Styles CSS

### Responsive Design

Les vidéos s'adaptent automatiquement à leur conteneur :

```scss
video {
  width: 100%;
  height: auto;
  max-width: 100%;
  object-fit: cover;
}
```

### Clip-path

Les vidéos héritent des mêmes clip-path que les images pour maintenir la cohérence visuelle.

## Emplacements des fichiers

- **Images** : `public/images/projects/[nom-projet]/`
- **Vidéos** : `public/videos/` ou `public/images/projects/[nom-projet]/`

## Exemple complet - Projet Copilote

```javascript
{
  id: "copilote",
  client: "Copilote",
  year: 2023,
  src: "videos/copilote-cards.webm",
  mediaType: "video",
  color: "#213638",
  visible: true,
  // ... autres propriétés
}
```

## Performance

- Les vidéos utilisent `preload="metadata"` pour optimiser le chargement
- `playsInline` évite le mode plein écran sur mobile
- `muted` permet l'autoplay sur tous les navigateurs

## Système Responsive (Nouveau !)

### Activation du mode responsive

```javascript
{
  id: "copilote",
  src: "videos/copilote-cards", // Sans extension
  mediaType: "video",
  responsive: true,
  videoSources: {
    mobile: { maxWidth: 640, quality: 'mobile' },
    tablet: { maxWidth: 1024, quality: 'tablet' },
    desktop: { maxWidth: 1920, quality: 'desktop' },
    large: { maxWidth: 2560, quality: 'large' }
  }
}
```

### Génération des résolutions

Utilisez le script fourni pour générer automatiquement toutes les résolutions :

```bash
# Génère copilote-cards-mobile.webm, copilote-cards-tablet.webm, etc.
node scripts/generate-video-sizes.js public/videos/copilote-cards.webm
```

### Structure des fichiers générés

```
public/videos/
├── copilote-cards.webm          # Original
├── copilote-cards-mobile.webm   # 640px
├── copilote-cards-mobile.mp4    # 640px (fallback)
├── copilote-cards-tablet.webm   # 1024px
├── copilote-cards-tablet.mp4    # 1024px (fallback)
├── copilote-cards-desktop.webm  # 1920px
├── copilote-cards-desktop.mp4   # 1920px (fallback)
├── copilote-cards-large.webm    # 2560px
└── copilote-cards-large.mp4     # 2560px (fallback)
```

### Avantages du système responsive

- 📱 **Mobile** : Vidéos légères (640px) pour économiser la bande passante
- 💻 **Desktop** : Qualité optimale (1920px+) pour les grands écrans
- 🔄 **Adaptation automatique** selon la taille d'écran
- 🚀 **Performance** : Chargement plus rapide sur mobile
- 🌐 **Compatibilité** : Fallback MP4 pour tous navigateurs

## Compatibilité

- ✅ Desktop (tous navigateurs modernes)
- ✅ Mobile (iOS/Android)
- ✅ Tablettes
- ✅ Mode grille et liste des projets
- ✅ Pages de détail des projets
- ✅ Composants Masonry
- ✅ Modals de prévisualisation
- ✅ **Nouveau** : Vidéos responsives multi-résolutions
