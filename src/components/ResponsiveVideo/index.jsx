'use client';
import { forwardRef, useState, useEffect } from 'react';

/**
 * Composant ResponsiveVideo - Équivalent de Next.js Image pour les vidéos
 * Gère différentes résolutions et formats selon la taille d'écran
 * 
 * @param {Object} props
 * @param {string} props.src - Chemin de base vers la vidéo (sans extension)
 * @param {Object} props.sources - Configuration des sources multiples
 * @param {number} props.width - Largeur de référence
 * @param {number} props.height - Hauteur de référence
 * @param {string} props.alt - Description pour l'accessibilité
 * @param {Object} props.style - Styles CSS
 * @param {string} props.className - Classes CSS
 * @param {boolean} props.autoPlay - Lecture automatique
 * @param {boolean} props.loop - Lecture en boucle
 * @param {boolean} props.muted - Vidéo muette
 * @param {boolean} props.controls - Afficher les contrôles
 * @param {string} props.objectFit - Comportement d'ajustement
 * @param {string} props.priority - Priorité de chargement ('high', 'low')
 */
const ResponsiveVideo = forwardRef(({
  src,
  sources = {},
  width,
  height,
  alt = '',
  style = {},
  className = '',
  autoPlay = true,
  loop = true,
  muted = true,
  controls = false,
  objectFit = 'cover',
  priority = 'low',
  poster,
  ...props
}, ref) => {
  const [currentSrc, setCurrentSrc] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // Configuration par défaut pour 480p et 720p
  const defaultSources = {
    mobile: { maxWidth: 768, quality: '480p' },
    desktop: { maxWidth: 9999, quality: '720p' }
  };

  // Merge avec les sources personnalisées
  const videoSources = { ...defaultSources, ...sources };

  // Génère les sources pour différentes résolutions
  const generateSources = () => {
    const sourcesArray = [];

    Object.entries(videoSources).forEach(([key, config]) => {
      const { maxWidth, quality, formats = ['webm', 'mp4'] } = config;

      formats.forEach(format => {
        // Assure-toi que le chemin commence par /
        const basePath = src.startsWith('/') ? src : `/${src}`;
        const videoSrc = `${basePath}-${quality}.${format}`;
        sourcesArray.push({
          src: videoSrc,
          type: `video/${format}`,
          media: `(max-width: ${maxWidth}px)`,
          quality
        });
      });
    });

    return sourcesArray.sort((a, b) => {
      const aWidth = parseInt(a.media.match(/\d+/)[0]);
      const bWidth = parseInt(b.media.match(/\d+/)[0]);
      return aWidth - bWidth;
    });
  };

  // Détecte la meilleure source selon la taille d'écran
  const getBestSource = () => {
    const basePath = src.startsWith('/') ? src : `/${src}`;
    if (typeof window === 'undefined') return `${basePath}-720p.webm`;

    const screenWidth = window.innerWidth;

    for (const [key, config] of Object.entries(videoSources)) {
      if (screenWidth <= config.maxWidth) {
        return `${basePath}-${config.quality}.webm`;
      }
    }

    return `${basePath}-720p.webm`;
  };

  // Met à jour la source selon la taille d'écran
  useEffect(() => {
    const updateSource = () => {
      setCurrentSrc(getBestSource());
    };

    updateSource();
    window.addEventListener('resize', updateSource);
    
    return () => window.removeEventListener('resize', updateSource);
  }, [src]);

  // Gestion du chargement
  const handleLoadStart = () => {
    setIsLoading(true);
  };

  const handleCanPlay = () => {
    setIsLoading(false);
  };

  const handleError = (e) => {
    console.error('Erreur de chargement vidéo:', e);
    setIsLoading(false);
  };

  const videoStyle = {
    width: style.width || '100%',
    height: style.height || 'auto',
    maxWidth: '100%',
    objectFit,
    display: 'block',
    opacity: isLoading ? 0.7 : 1,
    transition: 'opacity 0.3s ease',
    ...style
  };

  return (
    <div className={`responsive-video-container ${className}`} style={{ position: 'relative' }}>
      <video
        ref={ref}
        style={videoStyle}
        autoPlay={autoPlay}
        loop={loop}
        muted={muted}
        controls={controls}
        playsInline
        preload={priority === 'high' ? 'auto' : 'metadata'}
        poster={poster}
        onLoadStart={handleLoadStart}
        onCanPlay={handleCanPlay}
        onError={handleError}
        {...props}
      >
        {/* Sources multiples pour différentes résolutions */}
        {generateSources().map((source, index) => (
          <source
            key={index}
            src={source.src}
            type={source.type}
            media={source.media}
          />
        ))}
        
        {/* Fallback pour navigateurs non compatibles */}
        <source src={currentSrc} type="video/webm" />
        <source src={currentSrc.replace('.webm', '.mp4')} type="video/mp4" />
        
        {/* Message pour navigateurs très anciens */}
        <p>
          Votre navigateur ne supporte pas les vidéos HTML5.
          <a href={currentSrc} download>Télécharger la vidéo</a>
        </p>
      </video>
      
      {/* Indicateur de chargement optionnel */}
      {isLoading && (
        <div 
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            background: 'rgba(0,0,0,0.5)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '4px',
            fontSize: '14px',
            pointerEvents: 'none'
          }}
        >
          Chargement...
        </div>
      )}
    </div>
  );
});

ResponsiveVideo.displayName = 'ResponsiveVideo';

export default ResponsiveVideo;
