// components/project.jsx
'use client';
import Link from "next/link";
import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import MediaDisplay from "@/components/MediaDisplay";
import styles from "./style.module.scss";

export default function Project({
  index, title, client, expertise, year, location, slug, manageModal, src, srcVideo, mediaType, responsive, videoSources, locale = 'fr', viewMode = 'list'
}) {
  const [isHovered, setIsHovered] = useState(false);
  const [showVideo, setShowVideo] = useState(false);
  const loc = location || "—";
  const exp = expertise || "Design & Development";
  const yr  = year     || "—";

  // Timer pour afficher la vidéo après 1 seconde
  useEffect(() => {
    let timer;
    if (isHovered && viewMode === 'grid' && srcVideo) {
      timer = setTimeout(() => {
        setShowVideo(true);
      }, 1000);
    } else {
      setShowVideo(false);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [isHovered, viewMode, srcVideo]);

  // ➡️ Plus de index-based delay
  const containerVariants = {
    hidden: {},
    show: {
      transition: {
        staggerChildren: 0.15  // 150ms entre chaque colonne, dès que le projet est visible
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    show:  { y: 0, opacity: 1, transition: { duration: 0.6, ease: "easeOut" } }
  };

  // Classe CSS conditionnelle selon le viewMode
  const projectClass = `${styles.project} ${viewMode === 'grid' ? styles.gridMode : styles.listMode}`;

  // Gérer le hover selon le viewMode
  const handleMouseEnter = (e) => {
    setIsHovered(true);
    if (viewMode === 'grid') {
      // En vue grille : on active seulement la bulle "Explorer" (pas l'image modale)
      // On passe un index négatif pour indiquer qu'on veut seulement la bulle
      manageModal(true, -1, e.clientX, e.clientY);
    } else {
      // En vue liste : comportement normal avec image modale
      manageModal(true, index, e.clientX, e.clientY);
    }
  };

  const handleMouseLeave = (e) => {
    setIsHovered(false);
    // Dans tous les cas, on désactive au mouseLeave
    manageModal(false, index, e.clientX, e.clientY);
  };

  return (
    <Link
      href={`/${locale}/projets/${slug}`}
      className={projectClass}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* version desktop */}
      <motion.div
        className={styles.cols}
        variants={containerVariants}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true, amount: 0.1 }}
      >
        <motion.div variants={itemVariants} className={styles.colClient}>
          <h2 className={styles.clientLabel}>{client}</h2>
          <p className={styles.clientLabel}>{title}</p>
        </motion.div>

        <motion.div variants={itemVariants} className={styles.colLocation}>
          <p>{loc}</p>
        </motion.div>

        <motion.div variants={itemVariants} className={styles.colServices}>
          <p>{exp}</p>
        </motion.div>
      </motion.div>

      {/* version mobile */}
      <motion.div
        className={styles.gridLayout}
        variants={containerVariants}
        initial="hidden"
        whileInView="show"
        viewport={{ once: true, amount: 0.3 }}
      >
        <motion.div variants={itemVariants} className={styles.imageWrapper}>
          {src && (
            <div style={{ position: 'relative', width: '100%', height: 'auto' }}>
              {/* Image par défaut */}
              <MediaDisplay
                src={src}
                mediaType="image"
                alt={title}
                width={600}
                height={400}
                priority
                style={{
                  width: "100%",
                  height: "auto",
                  aspectRatio: "4/3",
                  objectFit: "cover",
                  display: (viewMode === 'grid' && showVideo) ? 'none' : 'block'
                }}
              />

              {/* Vidéo au survol (vue grille uniquement) */}
              {viewMode === 'grid' && showVideo && (
                <MediaDisplay
                  src={srcVideo}
                  mediaType="video"
                  responsive={responsive}
                  videoSources={videoSources}
                  alt={title}
                  width={600}
                  height={400}
                  style={{
                    width: "100%",
                    height: "auto",
                    aspectRatio: "4/3",
                    objectFit: "cover"
                  }}
                />
              )}
            </div>
          )}
        </motion.div>

        <motion.h3 variants={itemVariants} className={styles.projectTitle}>
          {title}
        </motion.h3>

        <motion.div variants={itemVariants} className={styles.separator} />

        <motion.div variants={itemVariants} className={styles.metaRow}>
          <div className={styles.services}>{client}</div>
          <div className={styles.year}>{yr}</div>
        </motion.div>
      </motion.div>
    </Link>
  );
}
