@import "@/styles/colors.scss";

.clientLabel, .colLocation, .colServices {
  color: $color-dark-brown;
}

.project {
  width: 100%;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
  color: inherit;
  display: block;

  &:hover,
  &:active,
  &:focus {
    text-decoration: none;
    outline: none;
    color: inherit;
  }

  /* --- Layout mobile en “grid” --- */
  .gridLayout {
    display: grid;
    padding: 20px 0;
    grid-template-columns: 1fr;

    .imageWrapper {
      width: 100%;

      /* Styles pour les vidéos dans la grille mobile */
      video {
        width: 100%;
        height: auto;
        border-radius: 8px;
      }
    }

    .projectTitle {
      margin: 0;
      padding-top: 25px;
      font-size: 24px;
      letter-spacing: -1px;
      word-spacing: 3px;
      font-weight: 400;
    }

    .separator {
      width: 100%;
      height: 1px;
      background-color: $color-light-gray;
      margin: calc(var(--gap-padding) / 2) 0;
    }

    .metaRow {
      display: flex;
      justify-content: space-between;

      .services,
      .year {
        font-weight: 300;
        font-size: 14px;
        text-align: center;
      }
    }
  }

  /* --- Layout desktop en colonnes --- */
  .cols {
    display: none; // caché en mobile
  }

  /* Styles desktop - Vue liste */
  &.listMode {
    @media (min-width: 1200px) {
      border-bottom: 1px solid rgb(201, 201, 201);

      &:first-child {
        border-top: 1px solid rgb(201, 201, 201);
      }

      .gridLayout {
        display: none;
      }

      .cols {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 30px 0;

        .colClient {
          width: 65%;
          padding-left: 69px;

          h2 {
            margin: 0;
            font-size: 60px;
            font-weight: 400;
            transition: all 0.4s;
            max-width: 90%;
          }

          .clientLabel {
            transition: all 0.4s;
            max-width: 90%;
          }
        }

        .colLocation {
          width: 25%;

          p {
            font-weight: 300;
            transition: all 0.4s;
          }
        }

        .colServices {
          width: 15%;

          p {
            font-weight: 300;
            transition: all 0.4s;
          }
        }
      }

      &:hover {
        opacity: 0.5;

        .clientLabel {
          transform: translateX(-10px);
        }
        p {
          transform: translateX(10px);
        }
      }
    }
  }

  /* Styles desktop - Vue grille (comme mobile) */
  &.gridMode {
    @media (min-width: 1200px) {
      border: none;

      .cols {
        display: none;
      }

      .gridLayout {
        display: block;
      }

      &:hover {
        opacity: 0.8;

        .clientLabel {
          transform: none;
        }
        p {
          transform: none;
        }
      }
    }
  }
}
