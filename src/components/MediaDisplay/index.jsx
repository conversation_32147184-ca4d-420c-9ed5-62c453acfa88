'use client';
import Image from 'next/image';
import { forwardRef } from 'react';
import ResponsiveVideo from '@/components/ResponsiveVideo';
import { useResponsiveVideo } from '@/hooks/useResponsiveVideo';

/**
 * Composant MediaDisplay - Affiche une image ou une vidéo selon le type de média
 * @param {Object} props
 * @param {string} props.src - Chemin vers le fichier média
 * @param {string} props.mediaType - Type de média ('image' ou 'video')
 * @param {string} props.alt - Texte alternatif
 * @param {number} props.width - Largeur
 * @param {number} props.height - Hauteur
 * @param {Object} props.style - Styles CSS
 * @param {string} props.className - Classes CSS
 * @param {boolean} props.priority - Priorité de chargement (pour les images)
 * @param {boolean} props.autoPlay - Lecture automatique (pour les vidéos)
 * @param {boolean} props.loop - Lecture en boucle (pour les vidéos)
 * @param {boolean} props.muted - Vidéo muette (pour les vidéos)
 * @param {boolean} props.controls - Afficher les contrôles (pour les vidéos)
 * @param {string} props.objectFit - Comportement d'ajustement
 * @param {boolean} props.responsive - Utiliser le système responsive pour les vidéos (défaut: true pour vidéos)
 * @param {Object} props.videoSources - Configuration des sources vidéo responsives
 */
const MediaDisplay = forwardRef(({
  src,
  mediaType = 'image',
  alt = '',
  width,
  height,
  style = {},
  className = '',
  priority = false,
  autoPlay = true,
  loop = true,
  muted = true,
  controls = false,
  objectFit = 'cover',
  responsive,
  videoSources = {},
  ...props
}, ref) => {
  // Détermine automatiquement le type de média si non spécifié
  const getMediaType = (src) => {
    if (mediaType !== 'image') return mediaType;
    
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov'];
    const extension = src.toLowerCase().substring(src.lastIndexOf('.'));
    return videoExtensions.includes(extension) ? 'video' : 'image';
  };

  const actualMediaType = getMediaType(src);

  // Responsive par défaut pour les vidéos, false pour les images
  const isResponsive = responsive !== undefined ? responsive : (actualMediaType === 'video');

  // Configuration par défaut pour 480, 720, 1080
  const defaultVideoSources = {
    mobile: { maxWidth: 768, quality: '480' },
    tablet: { maxWidth: 1200, quality: '720' },
    desktop: { maxWidth: 9999, quality: '1080' }
  };

  const finalVideoSources = Object.keys(videoSources).length > 0 ? videoSources : defaultVideoSources;

  // Gestion des chemins pour images et vidéos
  let mediaPath;
  if (actualMediaType === 'video') {
    // Pour les vidéos, on utilise le chemin tel quel ou on ajoute /videos/
    mediaPath = src.startsWith('/') ? src : (src.startsWith('videos/') ? `/${src}` : `/videos/${src}`);
  } else {
    // Pour les images, on utilise le système existant
    mediaPath = src.startsWith('/images/') ? src : `/images/${src}`;
  }

  if (actualMediaType === 'video') {
    // Utilise ResponsiveVideo si responsive est activé (par défaut pour les vidéos)
    if (isResponsive) {
      return (
        <ResponsiveVideo
          ref={ref}
          src={mediaPath.replace(/\.[^/.]+$/, "")} // Retire l'extension
          sources={finalVideoSources}
          width={width}
          height={height}
          alt={alt}
          style={style}
          className={className}
          autoPlay={autoPlay}
          loop={loop}
          muted={muted}
          controls={controls}
          objectFit={objectFit}
          priority={priority ? 'high' : 'low'}
          {...props}
        />
      );
    }

    // Vidéo standard (comportement actuel)
    return (
      <video
        ref={ref}
        src={mediaPath}
        width={width}
        height={height}
        style={{
          width: style.width || '100%',
          height: style.height || 'auto',
          maxWidth: '100%',
          objectFit,
          display: 'block',
          ...style
        }}
        className={className}
        autoPlay={autoPlay}
        loop={loop}
        muted={muted}
        controls={controls}
        playsInline
        preload="metadata"
        {...props}
      />
    );
  }

  // Pour les images, on utilise Next.js Image
  return (
    <Image
      ref={ref}
      src={mediaPath.startsWith('/images/') ? mediaPath : `/images/${src}`}
      alt={alt}
      width={width}
      height={height}
      style={{
        objectFit,
        ...style
      }}
      className={className}
      priority={priority}
      {...props}
    />
  );
});

MediaDisplay.displayName = 'MediaDisplay';

export default MediaDisplay;
