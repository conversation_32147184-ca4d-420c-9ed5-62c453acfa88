'use client';
import Image from 'next/image';
import { forwardRef } from 'react';

/**
 * Composant MediaDisplay - Affiche une image ou une vidéo selon le type de média
 * @param {Object} props
 * @param {string} props.src - Chemin vers le fichier média
 * @param {string} props.mediaType - Type de média ('image' ou 'video')
 * @param {string} props.alt - Texte alternatif
 * @param {number} props.width - Largeur
 * @param {number} props.height - Hauteur
 * @param {Object} props.style - Styles CSS
 * @param {string} props.className - Classes CSS
 * @param {boolean} props.priority - Priorité de chargement (pour les images)
 * @param {boolean} props.autoPlay - Lecture automatique (pour les vidéos)
 * @param {boolean} props.loop - Lecture en boucle (pour les vidéos)
 * @param {boolean} props.muted - Vidéo muette (pour les vidéos)
 * @param {boolean} props.controls - Afficher les contrôles (pour les vidéos)
 * @param {string} props.objectFit - Comportement d'ajustement
 */
const MediaDisplay = forwardRef(({
  src,
  mediaType = 'image',
  alt = '',
  width,
  height,
  style = {},
  className = '',
  priority = false,
  autoPlay = true,
  loop = true,
  muted = true,
  controls = false,
  objectFit = 'cover',
  ...props
}, ref) => {
  // Détermine automatiquement le type de média si non spécifié
  const getMediaType = (src) => {
    if (mediaType !== 'image') return mediaType;
    
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov'];
    const extension = src.toLowerCase().substring(src.lastIndexOf('.'));
    return videoExtensions.includes(extension) ? 'video' : 'image';
  };

  const actualMediaType = getMediaType(src);
  const mediaPath = src.startsWith('/') ? src : `/${src}`;

  if (actualMediaType === 'video') {
    return (
      <video
        ref={ref}
        src={mediaPath}
        width={width}
        height={height}
        style={{
          width: style.width || '100%',
          height: style.height || 'auto',
          maxWidth: '100%',
          objectFit,
          display: 'block',
          ...style
        }}
        className={className}
        autoPlay={autoPlay}
        loop={loop}
        muted={muted}
        controls={controls}
        playsInline
        preload="metadata"
        {...props}
      />
    );
  }

  // Pour les images, on utilise Next.js Image
  return (
    <Image
      ref={ref}
      src={mediaPath.startsWith('/images/') ? mediaPath : `/images/${src}`}
      alt={alt}
      width={width}
      height={height}
      style={{
        objectFit,
        ...style
      }}
      className={className}
      priority={priority}
      {...props}
    />
  );
});

MediaDisplay.displayName = 'MediaDisplay';

export default MediaDisplay;
